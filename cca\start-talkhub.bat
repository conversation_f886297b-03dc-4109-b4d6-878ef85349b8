@echo off
echo ========================================
echo    TalkHub Docker Startup Script
echo ========================================
echo.

echo Checking Docker status...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running
    echo Please install Docker Desktop and make sure it's running
    pause
    exit /b 1
)

echo Docker is available!
echo.

echo Checking if ports are available...
netstat -ano | findstr :3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo WARNING: Port 3000 is already in use
    echo Please close the application using port 3000 or use 'taskkill /PID [PID] /F'
)

netstat -ano | findstr :5000 >nul 2>&1
if %errorlevel% equ 0 (
    echo WARNING: Port 5000 is already in use
    echo Please close the application using port 5000 or use 'taskkill /PID [PID] /F'
)

echo NOTE: Using SQLite database for development (no PostgreSQL required)

echo.
echo Starting TalkHub services...
echo This may take a few minutes on first run...
echo.

docker-compose up --build

echo.
echo ========================================
echo TalkHub services have been stopped
echo ========================================
pause