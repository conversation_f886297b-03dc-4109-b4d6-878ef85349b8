import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { Button, theme } from '../styles/GlobalStyles';
import ThemeToggle from './ThemeToggle';
import { MenuIcon, CloseIcon } from './Icons';

const NavContainer = styled.nav`
  background: ${theme.colors.cardBackground};
  border-bottom: 1px solid ${theme.colors.border};
  box-shadow: ${theme.shadows.sm};
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
`;

const NavContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${theme.spacing.md};
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: 0 ${theme.spacing.lg};
  }
`;

const Logo = styled(Link)`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${theme.colors.primary};
  text-decoration: none;
  transition: color ${theme.transitions.fast};

  &:hover {
    color: ${theme.colors.primaryHover};
  }
`;

const NavLinks = styled.div<{ $isOpen: boolean }>`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};

  @media (max-width: ${theme.breakpoints.md}) {
    position: fixed;
    top: 64px;
    left: 0;
    right: 0;
    background: ${theme.colors.cardBackground};
    border-bottom: 1px solid ${theme.colors.border};
    flex-direction: column;
    padding: ${theme.spacing.lg};
    transform: translateY(${({ $isOpen }) => ($isOpen ? '0' : '-100%')});
    transition: transform ${theme.transitions.normal};
    box-shadow: ${theme.shadows.md};
    backdrop-filter: blur(10px);
  }
`;

const NavLink = styled(Link)`
  color: ${theme.colors.textSecondary};
  font-weight: 500;
  text-decoration: none;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  transition: all ${theme.transitions.fast};

  &:hover {
    color: ${theme.colors.textPrimary};
    background: ${theme.colors.gray100};
  }

  &.active {
    color: ${theme.colors.primary};
    background: ${theme.colors.primary}10;
  }
`;

const UserMenu = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
`;

const UserButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  padding: ${theme.spacing.sm};
  background: transparent;
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  color: ${theme.colors.textPrimary};
  font-weight: 500;
  transition: all ${theme.transitions.fast};

  &:hover {
    background: ${theme.colors.gray100};
    border-color: ${theme.colors.gray300};
  }
`;

const DropdownMenu = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  right: 0;
  background: ${theme.colors.cardBackground};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.lg};
  min-width: 200px;
  padding: ${theme.spacing.sm};
  transform: translateY(${({ $isOpen }) => ($isOpen ? '8px' : '0')});
  opacity: ${({ $isOpen }) => ($isOpen ? '1' : '0')};
  visibility: ${({ $isOpen }) => ($isOpen ? 'visible' : 'hidden')};
  transition: all ${theme.transitions.fast};
  z-index: 1000;
  backdrop-filter: blur(10px);
`;

const DropdownItem = styled.button`
  width: 100%;
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  background: transparent;
  border: none;
  border-radius: ${theme.borderRadius.sm};
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
  text-align: left;
  transition: background ${theme.transitions.fast};

  &:hover {
    background: ${theme.colors.surfaceBackground};
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: transparent;
  border: none;
  color: ${theme.colors.textPrimary};
  font-size: 1.25rem;
  padding: ${theme.spacing.sm};

  @media (max-width: ${theme.breakpoints.md}) {
    display: block;
  }
`;

const AuthButtons = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};

  @media (max-width: ${theme.breakpoints.md}) {
    flex-direction: column;
    width: 100%;
  }
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
`;

const Navigation: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { state, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    setIsUserMenuOpen(false);
    navigate('/');
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };



  return (
    <NavContainer>
      <NavContent>
        <Logo to="/" onClick={closeMobileMenu}>
          TalkHub
        </Logo>

        <NavLinks $isOpen={isMobileMenuOpen}>
          {state.isAuthenticated && (
            <>
              <NavLink to="/" onClick={closeMobileMenu}>
                Home
              </NavLink>
              <NavLink to="/subreddits" onClick={closeMobileMenu}>
                Communities
              </NavLink>
              <Button
                as={Link}
                to="/create-post"
                size="sm"
                onClick={closeMobileMenu}
              >
                Create Post
              </Button>
            </>
          )}

          {state.isAuthenticated ? (
            <UserMenu>
              <UserButton onClick={toggleUserMenu}>
                {state.user?.username}
              </UserButton>
              <DropdownMenu $isOpen={isUserMenuOpen}>
                <DropdownItem onClick={() => navigate('/profile')}>
                  Profile
                </DropdownItem>
                <DropdownItem onClick={handleLogout}>
                  Logout
                </DropdownItem>
              </DropdownMenu>
            </UserMenu>
          ) : (
            <AuthButtons>
              <Button
                as={Link}
                to="/login"
                variant="outline"
                size="sm"
                onClick={closeMobileMenu}
              >
                Sign In
              </Button>
              <Button
                as={Link}
                to="/register"
                size="sm"
                onClick={closeMobileMenu}
              >
                Sign Up
              </Button>
            </AuthButtons>
          )}
        </NavLinks>

        <RightSection>
          <ThemeToggle />
          <MobileMenuButton onClick={toggleMobileMenu}>
            {isMobileMenuOpen ? <CloseIcon size={20} /> : <MenuIcon size={20} />}
          </MobileMenuButton>
        </RightSection>
      </NavContent>
    </NavContainer>
  );
};

export default Navigation;
