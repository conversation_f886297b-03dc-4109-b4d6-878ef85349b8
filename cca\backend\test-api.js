// Simple API test script
// Run this after starting the server to test basic functionality

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAPI() {
  try {
    console.log('🚀 Testing TalkHub API...\n');

    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data.message);

    // Test user registration
    console.log('\n2. Testing user registration...');
    const registerData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const registerResponse = await axios.post(`${BASE_URL}/register`, registerData);
    console.log('✅ User registered:', registerResponse.data.user.username);
    const token = registerResponse.data.token;

    // Test user login
    console.log('\n3. Testing user login...');
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const loginResponse = await axios.post(`${BASE_URL}/login`, loginData);
    console.log('✅ User logged in:', loginResponse.data.user.username);

    // Test creating subreddit
    console.log('\n4. Testing subreddit creation...');
    const subredditData = {
      name: 'javascript',
      description: 'All things JavaScript'
    };
    
    const subredditResponse = await axios.post(`${BASE_URL}/subreddits`, subredditData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Subreddit created:', subredditResponse.data.subreddit.name);

    // Test getting subreddits
    console.log('\n5. Testing get subreddits...');
    const getSubredditsResponse = await axios.get(`${BASE_URL}/subreddits`);
    console.log('✅ Subreddits fetched:', getSubredditsResponse.data.subreddits.length, 'subreddits');

    console.log('\n🎉 All tests passed! TalkHub API is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };