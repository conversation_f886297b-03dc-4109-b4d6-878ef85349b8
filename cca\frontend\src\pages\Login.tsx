import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { Button, Input, Card, ErrorMessage, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { LoginRequest } from '../types';

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, ${theme.colors.primary}10, ${theme.colors.secondary}10);
  padding: ${theme.spacing.md};
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  padding: ${theme.spacing.xxl};
`;

const Title = styled.h1`
  text-align: center;
  margin-bottom: ${theme.spacing.xl};
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
`;

const Label = styled.label`
  font-weight: 500;
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
`;

const SubmitButton = styled(Button)`
  margin-top: ${theme.spacing.md};
  position: relative;
`;

const LinkContainer = styled.div`
  text-align: center;
  margin-top: ${theme.spacing.lg};
  color: ${theme.colors.textSecondary};
`;

const StyledLink = styled(Link)`
  color: ${theme.colors.primary};
  font-weight: 500;
  transition: color ${theme.transitions.fast};

  &:hover {
    color: ${theme.colors.primaryHover};
  }
`;

const Login: React.FC = () => {
  const [formData, setFormData] = useState<LoginRequest>({
    email: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const { state, login, clearError } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (state.isAuthenticated) {
      navigate('/');
    }
  }, [state.isAuthenticated, navigate]);

  useEffect(() => {
    clearError();
  }, [clearError]);

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await login(formData);
      navigate('/');
    } catch (error) {
      // Error is handled by the auth context
    }
  };

  return (
    <LoginContainer>
      <LoginCard>
        <Title>Welcome to TalkHub</Title>
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="email">Email</Label>
            <Input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              autoComplete="email"
            />
            {formErrors.email && <ErrorMessage>{formErrors.email}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">Password</Label>
            <Input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Enter your password"
              autoComplete="current-password"
            />
            {formErrors.password && <ErrorMessage>{formErrors.password}</ErrorMessage>}
          </FormGroup>

          {state.error && <ErrorMessage>{state.error}</ErrorMessage>}

          <SubmitButton
            type="submit"
            disabled={state.isLoading}
            $fullWidth
          >
            {state.isLoading ? <LoadingSpinner /> : 'Sign In'}
          </SubmitButton>
        </Form>

        <LinkContainer>
          Don't have an account?{' '}
          <StyledLink to="/register">Sign up here</StyledLink>
        </LinkContainer>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
