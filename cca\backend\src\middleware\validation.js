const validateRegistration = (req, res, next) => {
  const { username, email, password } = req.body;
  const errors = [];

  if (!username || username.trim().length < 3 || username.trim().length > 30) {
    errors.push('Username must be between 3 and 30 characters');
  }

  if (!username || !/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('Username can only contain letters, numbers, and underscores');
  }

  if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('Please provide a valid email address');
  }

  if (!password || password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validateLogin = (req, res, next) => {
  const { email, password } = req.body;
  const errors = [];

  if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('Please provide a valid email address');
  }

  if (!password) {
    errors.push('Password is required');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validateSubreddit = (req, res, next) => {
  const { name, description } = req.body;
  const errors = [];

  if (!name || name.trim().length < 3 || name.trim().length > 50) {
    errors.push('Subreddit name must be between 3 and 50 characters');
  }

  if (!name || !/^[a-zA-Z0-9_]+$/.test(name)) {
    errors.push('Subreddit name can only contain letters, numbers, and underscores');
  }

  if (description && description.length > 500) {
    errors.push('Description cannot exceed 500 characters');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validatePost = (req, res, next) => {
  const { title, body, subredditId } = req.body;
  const errors = [];

  if (!title || title.trim().length < 5 || title.trim().length > 300) {
    errors.push('Post title must be between 5 and 300 characters');
  }

  if (!subredditId || !Number.isInteger(Number(subredditId))) {
    errors.push('Valid subreddit ID is required');
  }

  if (body && body.length > 10000) {
    errors.push('Post body cannot exceed 10,000 characters');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validateComment = (req, res, next) => {
  const { content, postId, parentCommentId } = req.body;
  const errors = [];

  if (!content || content.trim().length < 1 || content.trim().length > 10000) {
    errors.push('Comment content must be between 1 and 10,000 characters');
  }

  if (!postId || !Number.isInteger(Number(postId))) {
    errors.push('Valid post ID is required');
  }

  if (parentCommentId && !Number.isInteger(Number(parentCommentId))) {
    errors.push('Parent comment ID must be a valid integer');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validateVote = (req, res, next) => {
  const { value } = req.body;
  const errors = [];

  if (value === undefined || ![1, -1].includes(Number(value))) {
    errors.push('Vote value must be 1 (upvote) or -1 (downvote)');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

module.exports = {
  validateRegistration,
  validateLogin,
  validateSubreddit,
  validatePost,
  validateComment,
  validateVote
};
