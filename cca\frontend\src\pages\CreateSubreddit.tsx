import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Container, Card, Button, Input, TextArea, ErrorMessage, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { CreateSubredditRequest } from '../types';
import { apiService } from '../services/api';

const CreateContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
  max-width: 600px;
`;

const Title = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: ${theme.spacing.xl};
  text-align: center;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
`;

const Label = styled.label`
  font-weight: 500;
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
`;

const HelpText = styled.div`
  font-size: 0.75rem;
  color: ${theme.colors.textMuted};
  margin-top: ${theme.spacing.xs};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${theme.spacing.lg};

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
  }
`;

const CreateSubreddit: React.FC = () => {
  const [formData, setFormData] = useState<CreateSubredditRequest>({
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.name) {
      errors.name = 'Community name is required';
    } else if (formData.name.length < 3) {
      errors.name = 'Community name must be at least 3 characters';
    } else if (formData.name.length > 50) {
      errors.name = 'Community name must be less than 50 characters';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.name)) {
      errors.name = 'Community name can only contain letters, numbers, and underscores';
    }

    if (formData.description && formData.description.length > 500) {
      errors.description = 'Description must be less than 500 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }

    // Clear general error
    if (error) {
      setError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.createSubreddit(formData);
      navigate(`/r/${response.subreddit.name}`);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create community');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/');
  };

  return (
    <CreateContainer>
      <Card>
        <Title>Create a Community</Title>
        
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="name">Community Name</Label>
            <Input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter community name"
              maxLength={50}
            />
            <HelpText>
              Community names can only contain letters, numbers, and underscores. 
              Choose wisely - this cannot be changed later!
            </HelpText>
            {formErrors.name && <ErrorMessage>{formErrors.name}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="description">Description (Optional)</Label>
            <TextArea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Tell people what this community is about..."
              maxLength={500}
              rows={4}
            />
            <HelpText>
              {formData.description.length}/500 characters
            </HelpText>
            {formErrors.description && <ErrorMessage>{formErrors.description}</ErrorMessage>}
          </FormGroup>

          {error && <ErrorMessage>{error}</ErrorMessage>}

          <ButtonGroup>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.name.trim()}
            >
              {isLoading ? <LoadingSpinner /> : 'Create Community'}
            </Button>
          </ButtonGroup>
        </Form>
      </Card>
    </CreateContainer>
  );
};

export default CreateSubreddit;
