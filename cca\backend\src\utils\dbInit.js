const mongoose = require('mongoose');
const { User, Subreddit, Post, Comment, Vote } = require('../models');

/**
 * Initialize database with default data
 */
const initializeDatabase = async () => {
  try {
    // Ensure connection is ready
    if (mongoose.connection.readyState !== 1) {
      console.log('⏳ Waiting for database connection...');
      await new Promise((resolve, reject) => {
        if (mongoose.connection.readyState === 1) {
          resolve();
        } else {
          mongoose.connection.once('connected', resolve);
          mongoose.connection.once('error', reject);
          // Timeout after 10 seconds
          setTimeout(() => reject(new Error('Database connection timeout')), 10000);
        }
      });
    }

    // Create default subreddits
    const defaultSubreddits = [
      {
        name: 'general',
        description: 'General discussion for all topics',
        isDefault: true
      },
      {
        name: 'announcements',
        description: 'Official announcements and updates',
        isDefault: true
      },
      {
        name: 'help',
        description: 'Get help and support from the community',
        isDefault: true
      },
      {
        name: 'feedback',
        description: 'Share your feedback and suggestions',
        isDefault: true
      }
    ];

    console.log('📝 Creating default subreddits...');
    // Insert subreddits one by one to handle duplicates gracefully
    let createdCount = 0;
    for (const subredditData of defaultSubreddits) {
      try {
        const existingSubreddit = await Subreddit.findOne({ name: subredditData.name });
        if (!existingSubreddit) {
          await Subreddit.create(subredditData);
          console.log(`✅ Created subreddit: ${subredditData.name}`);
          createdCount++;
        } else {
          console.log(`ℹ️ Subreddit already exists: ${subredditData.name}`);
        }
      } catch (error) {
        console.log(`⚠️ Error creating subreddit ${subredditData.name}:`, error.message);
      }
    }
    console.log(`✅ Created ${createdCount} new subreddits`);

    // Create indexes for better performance
    console.log('🔍 Creating database indexes...');

    // User indexes
    await User.collection.createIndex({ username: 1 }, { unique: true });
    await User.collection.createIndex({ email: 1 }, { unique: true });

    // Subreddit indexes
    await Subreddit.collection.createIndex({ name: 1 }, { unique: true });

    // Post indexes
    await Post.collection.createIndex({ userId: 1 });
    await Post.collection.createIndex({ subredditId: 1 });
    await Post.collection.createIndex({ createdAt: -1 });
    await Post.collection.createIndex({ title: 'text', body: 'text' });

    // Comment indexes
    await Comment.collection.createIndex({ postId: 1 });
    await Comment.collection.createIndex({ userId: 1 });
    await Comment.collection.createIndex({ parentId: 1 });
    await Comment.collection.createIndex({ createdAt: -1 });

    // Vote indexes
    await Vote.collection.createIndex({ userId: 1, postId: 1 }, { unique: true });
    await Vote.collection.createIndex({ userId: 1, commentId: 1 }, { unique: true });

    console.log('✅ Database indexes created successfully');
    console.log('🎉 Database initialization completed!');

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
};

/**
 * Clean up test data and reset database
 */
const cleanDatabase = async () => {
  try {
    console.log('🧹 Cleaning database...');

    await Vote.deleteMany({});
    await Comment.deleteMany({});
    await Post.deleteMany({});
    await Subreddit.deleteMany({});
    await User.deleteMany({});

    console.log('✅ Database cleaned successfully');
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
    throw error;
  }
};

/**
 * Validate database schema and connections
 */
const validateDatabase = async () => {
  try {
    console.log('🔍 Validating database...');

    // Check connection
    if (mongoose.connection.readyState !== 1) {
      throw new Error('Database not connected');
    }

    // Validate collections exist
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    const requiredCollections = ['users', 'subreddits', 'posts', 'comments', 'votes'];
    const missingCollections = requiredCollections.filter(name => !collectionNames.includes(name));

    if (missingCollections.length > 0) {
      console.log(`📝 Missing collections: ${missingCollections.join(', ')}`);
      console.log('🔄 Collections will be created when first document is inserted');
    }

    // Test basic operations
    const testUser = new User({
      username: 'validation_test_user',
      email: '<EMAIL>',
      password: 'test123'
    });

    await testUser.validate();
    console.log('✅ User model validation passed');

    console.log('✅ Database validation completed');

    return {
      status: 'valid',
      collections: collectionNames,
      missingCollections
    };

  } catch (error) {
    console.error('❌ Database validation failed:', error);
    return {
      status: 'invalid',
      error: error.message
    };
  }
};

module.exports = {
  initializeDatabase,
  cleanDatabase,
  validateDatabase
};