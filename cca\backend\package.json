{"name": "talkhub-backend", "version": "1.0.0", "description": "Reddit-style backend API built with Node.js, Express, PostgreSQL, and JWT authentication", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "node test-api.js", "docker:build": "docker build -t talkhub-backend .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down"}, "keywords": ["reddit", "api", "nodejs", "express", "postgresql", "jwt"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3"}, "devDependencies": {"axios": "^1.6.2", "nodemon": "^3.0.2"}}