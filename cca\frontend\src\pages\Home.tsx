import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { Container, Card, But<PERSON>, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { Subreddit } from '../types';
import { apiService } from '../services/api';

const HomeContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${theme.spacing.xl};
  flex-wrap: wrap;
  gap: ${theme.spacing.md};
`;

const Title = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
`;

const SubredditGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.xl};
`;

const SubredditCard = styled(Card)`
  transition: all ${theme.transitions.fast};
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.lg};
  }
`;

const SubredditHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  margin-bottom: ${theme.spacing.md};
`;

const SubredditIcon = styled.div`
  width: 40px;
  height: 40px;
  background: ${theme.colors.primary};
  border-radius: ${theme.borderRadius.full};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${theme.colors.white};
  font-weight: 700;
  font-size: 1.125rem;
`;

const SubredditName = styled.h3`
  color: ${theme.colors.textPrimary};
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
`;

const SubredditDescription = styled.p`
  color: ${theme.colors.textSecondary};
  margin: 0 0 ${theme.spacing.md} 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const SubredditMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  color: ${theme.colors.textMuted};
  font-size: 0.875rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  flex-direction: column;
  gap: ${theme.spacing.md};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing.xxl};
  color: ${theme.colors.textSecondary};
`;

const EmptyStateTitle = styled.h3`
  color: ${theme.colors.textPrimary};
  margin-bottom: ${theme.spacing.md};
`;

const ErrorMessage = styled.div`
  background: ${theme.colors.danger}10;
  color: ${theme.colors.danger};
  padding: ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  margin-bottom: ${theme.spacing.lg};
  text-align: center;
`;

const Home: React.FC = () => {
  const [subreddits, setSubreddits] = useState<Subreddit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSubreddits();
  }, []);

  const fetchSubreddits = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getSubreddits();
      setSubreddits(response.subreddits);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load communities');
    } finally {
      setIsLoading(false);
    }
  };

  const getSubredditInitial = (name: string) => {
    return name.charAt(0).toUpperCase();
  };

  if (isLoading) {
    return (
      <HomeContainer>
        <LoadingContainer>
          <LoadingSpinner />
          <div>Loading communities...</div>
        </LoadingContainer>
      </HomeContainer>
    );
  }

  return (
    <HomeContainer>
      <Header>
        <Title>Communities</Title>
        <Button as={Link} to="/create-subreddit">
          Create Community
        </Button>
      </Header>

      {error && (
        <ErrorMessage>
          {error}
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchSubreddits}
            style={{ marginLeft: theme.spacing.md }}
          >
            Try Again
          </Button>
        </ErrorMessage>
      )}

      {subreddits.length === 0 && !isLoading && !error ? (
        <EmptyState>
          <EmptyStateTitle>No communities yet</EmptyStateTitle>
          <p>Be the first to create a community and start the conversation!</p>
          <Button as={Link} to="/create-subreddit" style={{ marginTop: theme.spacing.lg }}>
            Create Community
          </Button>
        </EmptyState>
      ) : (
        <SubredditGrid>
          {subreddits.map((subreddit) => (
            <SubredditCard
              key={subreddit.id}
              as={Link}
              to={`/r/${subreddit.name}`}
            >
              <SubredditHeader>
                <SubredditIcon>
                  {getSubredditInitial(subreddit.name)}
                </SubredditIcon>
                <SubredditName>r/{subreddit.name}</SubredditName>
              </SubredditHeader>

              <SubredditDescription>
                {subreddit.description || 'No description available'}
              </SubredditDescription>

              <SubredditMeta>
                <span>Community</span>
              </SubredditMeta>
            </SubredditCard>
          ))}
        </SubredditGrid>
      )}
    </HomeContainer>
  );
};

export default Home;
