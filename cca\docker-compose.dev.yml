# Development overrides for docker-compose.yml
# This file is automatically loaded by docker-compose

version: '3.8'

services:
  backend:
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for development (hot reload)
      - ./backend/src:/app/src:ro
    command: ["npm", "run", "dev"]

  frontend:
    environment:
      - REACT_APP_API_URL=http://localhost:5000/api
    # For development, we can mount source code if needed
    # volumes:
    #   - ./frontend/src:/app/src:ro
