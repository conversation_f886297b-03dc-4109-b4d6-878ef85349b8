// User types
export interface User {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  message: string;
  token: string;
  user: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// Subreddit types
export interface Subreddit {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubredditRequest {
  name: string;
  description: string;
}

// Post types
export interface Post {
  id: number;
  title: string;
  body?: string;
  userId: number;
  subredditId: number;
  createdAt: string;
  updatedAt: string;
  author: {
    id: number;
    username: string;
  };
  subreddit: {
    id: number;
    name: string;
  };
  score?: number;
  upvotes?: number;
  downvotes?: number;
  totalVotes?: number;
}

export interface CreatePostRequest {
  title: string;
  body?: string;
  subredditId: number;
}

// Comment types
export interface Comment {
  id: number;
  content: string;
  postId: number;
  userId: number;
  parentCommentId?: number;
  createdAt: string;
  updatedAt: string;
  author: {
    id: number;
    username: string;
  };
  replies: Comment[];
}

export interface CreateCommentRequest {
  content: string;
  postId: number;
  parentCommentId?: number;
}

// Vote types
export interface Vote {
  id: number;
  userId: number;
  postId: number;
  value: 1 | -1;
  createdAt: string;
  updatedAt: string;
}

export interface VoteRequest {
  value: 1 | -1;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
  details?: string;
}

export interface PostWithComments {
  post: Post;
  comments: Comment[];
}

export interface SubredditWithPosts {
  subreddit: Subreddit;
  posts: Post[];
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface AuthState extends LoadingState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
}

// Form types
export interface FormErrors {
  [key: string]: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: FormErrors;
}
