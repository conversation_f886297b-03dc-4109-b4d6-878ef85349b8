const mongoose = require('mongoose');
require('dotenv').config();

const connectDB = require('./src/config/db');
const { checkDBHealth } = require('./src/config/db');
const { User, Subreddit, Post, Comment, Vote } = require('./src/models');

async function testMongoDB() {
  try {
    console.log('Testing MongoDB connection and models...');
    
    // Connect to MongoDB
    await connectDB();

    // Check database health
    const dbHealth = await checkDBHealth();
    console.log('Database health:', dbHealth);
    
    // Test User model
    console.log('\n1. Testing User model...');
    const testUser = new User({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    });
    
    await testUser.save();
    console.log('✓ User created successfully');
    
    // Test Subreddit model
    console.log('\n2. Testing Subreddit model...');
    const testSubreddit = new Subreddit({
      name: 'testsubreddit',
      description: 'A test subreddit'
    });
    
    await testSubreddit.save();
    console.log('✓ Subreddit created successfully');
    
    // Test Post model
    console.log('\n3. Testing Post model...');
    const testPost = new Post({
      title: 'Test Post Title',
      body: 'This is a test post body',
      userId: testUser._id,
      subredditId: testSubreddit._id
    });
    
    await testPost.save();
    console.log('✓ Post created successfully');
    
    // Test Comment model
    console.log('\n4. Testing Comment model...');
    const testComment = new Comment({
      content: 'This is a test comment',
      postId: testPost._id,
      userId: testUser._id
    });
    
    await testComment.save();
    console.log('✓ Comment created successfully');
    
    // Test Vote model
    console.log('\n5. Testing Vote model...');
    const testVote = new Vote({
      userId: testUser._id,
      postId: testPost._id,
      value: 1
    });
    
    await testVote.save();
    console.log('✓ Vote created successfully');
    
    // Test queries with population
    console.log('\n6. Testing queries with population...');
    const populatedPost = await Post.findById(testPost._id)
      .populate('userId', 'username email')
      .populate('subredditId', 'name description');
    
    console.log('✓ Post populated successfully:', {
      title: populatedPost.title,
      author: populatedPost.userId.username,
      subreddit: populatedPost.subredditId.name
    });
    
    // Clean up test data
    console.log('\n7. Cleaning up test data...');
    await Vote.findByIdAndDelete(testVote._id);
    await Comment.findByIdAndDelete(testComment._id);
    await Post.findByIdAndDelete(testPost._id);
    await Subreddit.findByIdAndDelete(testSubreddit._id);
    await User.findByIdAndDelete(testUser._id);
    console.log('✓ Test data cleaned up successfully');
    
    console.log('\n🎉 All MongoDB tests passed successfully!');
    console.log('✓ Database connection working');
    console.log('✓ All models working correctly');
    console.log('✓ Population queries working');
    console.log('✓ CRUD operations working');
    
  } catch (error) {
    console.error('❌ MongoDB test failed:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\nDatabase connection closed.');
    process.exit(0);
  }
}

testMongoDB();