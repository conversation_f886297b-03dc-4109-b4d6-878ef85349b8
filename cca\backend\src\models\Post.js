const mongoose = require('mongoose');

const postSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    minlength: 5,
    maxlength: 300
  },
  body: {
    type: String,
    default: ''
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  subredditId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subreddit',
    required: true
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Post', postSchema);