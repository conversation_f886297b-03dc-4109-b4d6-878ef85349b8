# TalkHub Backend API

A Reddit-style backend API built with Node.js, Express, PostgreSQL, Sequelize, and JWT authentication. This is Phase 1 of the TalkHub project, providing core functionality for user authentication, subreddit management, posts, comments, and voting.

## 🚀 Tech Stack

- **Node.js** - JavaScript runtime
- **Express.js** - Web framework
- **PostgreSQL** - Database
- **Sequelize** - ORM
- **JWT** - Authentication
- **bcryptjs** - Password hashing
- **Docker** - Containerization
- **Render** - Cloud deployment

## 📁 Project Structure

```
talkhub-backend/
├── src/
│   ├── config/
│   │   └── db.js              # Database configuration
│   ├── controllers/
│   │   ├── authController.js   # Authentication logic
│   │   ├── subredditController.js
│   │   ├── postController.js
│   │   └── commentController.js
│   ├── middleware/
│   │   └── auth.js            # JWT authentication middleware
│   ├── models/
│   │   ├── index.js           # Model associations
│   │   ├── User.js
│   │   ├── Subreddit.js
│   │   ├── Post.js
│   │   ├── Comment.js
│   │   └── Vote.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── subreddits.js
│   │   ├── posts.js
│   │   └── comments.js
│   └── app.js                 # Main application file
├── .env                       # Environment variables
├── Dockerfile                 # Docker configuration
├── docker-compose.yml         # Docker Compose setup
├── package.json
└── README.md
```

## 🗄️ Database Models

### User
- `id` (Primary Key)
- `username` (Unique)
- `email` (Unique)
- `password` (Hashed)
- `createdAt`, `updatedAt`

### Subreddit
- `id` (Primary Key)
- `name` (Unique)
- `description`
- `createdAt`, `updatedAt`

### Post
- `id` (Primary Key)
- `title`
- `body`
- `userId` (Foreign Key)
- `subredditId` (Foreign Key)
- `createdAt`, `updatedAt`

### Comment
- `id` (Primary Key)
- `content`
- `postId` (Foreign Key)
- `userId` (Foreign Key)
- `parentCommentId` (Foreign Key, nullable for threaded comments)
- `createdAt`, `updatedAt`

### Vote
- `id` (Primary Key)
- `userId` (Foreign Key)
- `postId` (Foreign Key)
- `value` (1 for upvote, -1 for downvote)
- `createdAt`, `updatedAt`

## 🔗 API Endpoints

### Authentication
- `POST /api/register` - Register a new user
- `POST /api/login` - Login user

### Subreddits
- `POST /api/subreddits` - Create subreddit (Auth required)
- `GET /api/subreddits` - List all subreddits
- `GET /api/subreddits/:name/posts` - Get posts in a subreddit

### Posts
- `POST /api/posts` - Create post (Auth required)
- `GET /api/posts/:id/comments` - Get post with comments
- `POST /api/posts/:id/vote` - Vote on post (Auth required)

### Comments
- `POST /api/comments` - Create comment (Auth required)

### Health Check
- `GET /api/health` - API health status

## 🛠️ Local Development Setup

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL (v14 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd talkhub-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Update the `.env` file with your database credentials:
   ```env
   DB_HOST=localhost
   DB_USER=your_postgres_user
   DB_PASSWORD=your_postgres_password
   DB_NAME=talkhub
   DB_PORT=5432
   JWT_SECRET=your_super_secret_jwt_key
   PORT=5000
   NODE_ENV=development
   ```

4. **Set up PostgreSQL database**
   ```sql
   CREATE DATABASE talkhub;
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

The API will be available at `http://localhost:5000`

## 🐳 Docker Setup

### Using Docker Compose (Recommended)

1. **Start the application with database**
   ```bash
   docker-compose up -d
   ```

2. **View logs**
   ```bash
   docker-compose logs -f
   ```

3. **Stop the application**
   ```bash
   docker-compose down
   ```

### Using Docker only

1. **Build the image**
   ```bash
   docker build -t talkhub-backend .
   ```

2. **Run the container**
   ```bash
   docker run -p 5000:5000 --env-file .env talkhub-backend
   ```

## ☁️ Deployment to Render

### Prerequisites
- GitHub account
- Render account

### Steps

1. **Push code to GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin <your-github-repo-url>
   git push -u origin main
   ```

2. **Create PostgreSQL Database on Render**
   - Go to Render Dashboard
   - Click "New" → "PostgreSQL"
   - Choose a name (e.g., `talkhub-db`)
   - Select region and plan
   - Note the connection details

3. **Create Web Service on Render**
   - Click "New" → "Web Service"
   - Connect your GitHub repository
   - Configure:
     - **Name**: `talkhub-api`
     - **Environment**: `Node`
     - **Build Command**: `npm install`
     - **Start Command**: `npm start`
     - **Node Version**: `18`

4. **Set Environment Variables**
   Add these environment variables in Render:
   ```
   DB_HOST=<your-render-postgres-host>
   DB_USER=<your-render-postgres-user>
   DB_PASSWORD=<your-render-postgres-password>
   DB_NAME=<your-render-postgres-database>
   DB_PORT=5432
   JWT_SECRET=<your-secure-jwt-secret>
   NODE_ENV=production
   ```

5. **Deploy**
   - Click "Create Web Service"
   - Wait for deployment to complete
   - Your API will be available at: `https://your-service-name.onrender.com`

## 🧪 API Testing

### Using Postman

1. **Register a new user**
   ```
   POST https://your-api-url/api/register
   Content-Type: application/json

   {
     "username": "testuser",
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

2. **Login**
   ```
   POST https://your-api-url/api/login
   Content-Type: application/json

   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

3. **Create a subreddit** (requires token)
   ```
   POST https://your-api-url/api/subreddits
   Content-Type: application/json
   Authorization: Bearer <your-jwt-token>

   {
     "name": "javascript",
     "description": "All things JavaScript"
   }
   ```

4. **Create a post** (requires token)
   ```
   POST https://your-api-url/api/posts
   Content-Type: application/json
   Authorization: Bearer <your-jwt-token>

   {
     "title": "My first post",
     "body": "This is the content of my post",
     "subredditId": 1
   }
   ```

5. **Vote on a post** (requires token)
   ```
   POST https://your-api-url/api/posts/1/vote
   Content-Type: application/json
   Authorization: Bearer <your-jwt-token>

   {
     "value": 1
   }
   ```

### Example Responses

**Successful Registration:**
```json
{
  "message": "User registered successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}
```

**Get Subreddits:**
```json
{
  "subreddits": [
    {
      "id": 1,
      "name": "javascript",
      "description": "All things JavaScript",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## 🔒 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

Protected endpoints require a valid JWT token. Tokens expire after 7 days.

## 🚦 Error Handling

The API returns consistent error responses:

```json
{
  "error": "Error message",
  "details": "Additional error details (in development mode)"
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the ISC License.

## 🔮 Future Enhancements (Phase 2+)

- User profiles and avatars
- Image/file uploads for posts
- Real-time notifications
- Advanced search functionality
- Moderation tools
- Rate limiting
- Caching with Redis
- Email verification
- Password reset functionality
- Admin dashboard