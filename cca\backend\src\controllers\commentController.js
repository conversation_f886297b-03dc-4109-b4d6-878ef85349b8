const { Comment, User, Post } = require('../models');

const createComment = async (req, res) => {
  try {
    const { content, postId, parentCommentId } = req.body;
    const userId = req.user.id;

    // Check if post exists
    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // If parentCommentId is provided, check if parent comment exists
    if (parentCommentId) {
      const parentComment = await Comment.findById(parentCommentId);
      if (!parentComment) {
        return res.status(404).json({ error: 'Parent comment not found' });
      }
      // Ensure parent comment belongs to the same post
      if (parentComment.postId.toString() !== postId) {
        return res.status(400).json({ error: 'Parent comment does not belong to this post' });
      }
    }

    const comment = new Comment({
      content,
      postId,
      userId,
      parentCommentId: parentCommentId || null
    });

    await comment.save();

    // Populate the created comment with associations
    const createdComment = await Comment.findById(comment._id)
      .populate('userId', 'username')
      .populate('parentCommentId', 'content');

    res.status(201).json({
      message: 'Comment created successfully',
      comment: createdComment
    });
  } catch (error) {
    console.error('Create comment error:', error);
    res.status(500).json({
      error: 'Failed to create comment',
      details: error.message
    });
  }
};

module.exports = {
  createComment
};