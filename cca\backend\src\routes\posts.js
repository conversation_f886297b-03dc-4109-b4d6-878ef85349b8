const express = require('express');
const { createPost, getPostComments, voteOnPost } = require('../controllers/postController');
const auth = require('../middleware/auth');
const { validatePost, validateVote } = require('../middleware/validation');

const router = express.Router();

// POST /api/posts (create post) - requires auth
router.post('/', auth, validatePost, createPost);

// GET /api/posts/:id/comments
router.get('/:id/comments', getPostComments);

// POST /api/posts/:id/vote (upvote/downvote) - requires auth
router.post('/:id/vote', auth, validateVote, voteOnPost);

module.exports = router;