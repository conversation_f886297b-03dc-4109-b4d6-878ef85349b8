import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../contexts/ThemeContext';
import { theme } from '../styles/GlobalStyles';
import { SunIcon, MoonIcon } from './Icons';

const ToggleButton = styled.button<{ $isDark: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: ${theme.borderRadius.full};
  border: 1px solid ${theme.colors.border};
  background: ${theme.colors.cardBackground};
  color: ${theme.colors.textPrimary};
  cursor: pointer;
  transition: all ${theme.transitions.normal};
  position: relative;
  overflow: hidden;

  &:hover {
    background: ${theme.colors.surfaceBackground};
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  svg {
    width: 18px;
    height: 18px;
    transition: all ${theme.transitions.normal};
  }
`;

const IconWrapper = styled.div<{ $isVisible: boolean }>`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${({ $isVisible }) => ($isVisible ? 1 : 0)};
  transform: ${({ $isVisible }) => 
    $isVisible ? 'translateY(0) rotate(0deg)' : 'translateY(20px) rotate(180deg)'};
  transition: all ${theme.transitions.normal};
`;

const TooltipWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

const Tooltip = styled.div<{ $show: boolean }>`
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: ${theme.colors.dark};
  color: ${theme.colors.white};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  border-radius: ${theme.borderRadius.sm};
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: ${({ $show }) => ($show ? 1 : 0)};
  visibility: ${({ $show }) => ($show ? 'visible' : 'hidden')};
  transition: all ${theme.transitions.fast};
  margin-bottom: ${theme.spacing.xs};
  z-index: 1000;

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: ${theme.colors.dark};
  }
`;

interface ThemeToggleProps {
  className?: string;
  showTooltip?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className,
  showTooltip = true
}) => {
  const { state, toggleTheme } = useTheme();
  const [showTooltipState, setShowTooltipState] = React.useState(false);

  const handleMouseEnter = () => {
    if (showTooltip) {
      setShowTooltipState(true);
    }
  };

  const handleMouseLeave = () => {
    setShowTooltipState(false);
  };



  return (
    <TooltipWrapper className={className}>
      <ToggleButton
        $isDark={state.isDarkMode}
        onClick={toggleTheme}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        aria-label={`Switch to ${state.isDarkMode ? 'light' : 'dark'} mode`}
        title={`Switch to ${state.isDarkMode ? 'light' : 'dark'} mode`}
      >
        <IconWrapper $isVisible={!state.isDarkMode}>
          <SunIcon size={18} />
        </IconWrapper>
        <IconWrapper $isVisible={state.isDarkMode}>
          <MoonIcon size={18} />
        </IconWrapper>
      </ToggleButton>

      {showTooltip && (
        <Tooltip $show={showTooltipState}>
          {state.isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
        </Tooltip>
      )}
    </TooltipWrapper>
  );
};

export default ThemeToggle;
