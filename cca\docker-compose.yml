version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: talkhub-mongodb
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=talkhub
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./backend/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - talkhub-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Backend API (using MongoDB)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: talkhub-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - PORT=5000
      - MONGODB_URI=******************************************************************
      - JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_minimum_32_characters
      - JWT_EXPIRES_IN=7d
      - CORS_ORIGIN=http://localhost:3000
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - LOG_LEVEL=info
    ports:
      - "5000:5000"
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - talkhub-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: talkhub-frontend
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://backend:5000/api
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - talkhub-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: talkhub-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redispassword123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - talkhub-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Nginx Reverse Proxy
  nginx-proxy:
    image: nginx:alpine
    container_name: talkhub-nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      frontend:
        condition: service_healthy
      backend:
        condition: service_healthy
    networks:
      - talkhub-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: talkhub-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - talkhub-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  talkhub-network:
    driver: bridge
    name: talkhub-network

volumes:
  mongodb_data:
    driver: local
    name: talkhub-mongodb-data
  redis_data:
    driver: local
    name: talkhub-redis-data
  nginx_logs:
    driver: local
    name: talkhub-nginx-logs
  prometheus_data:
    driver: local
    name: talkhub-prometheus-data