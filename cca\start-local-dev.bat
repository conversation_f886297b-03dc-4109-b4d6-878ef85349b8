@echo off
echo ========================================
echo    TalkHub Local Development Startup
echo    (SQLite - No Docker Required)
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js is available!
echo.

echo Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo Installing frontend dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo Starting backend server (SQLite)...
cd ..\backend
start "TalkHub Backend" cmd /k "npm run dev"

echo.
echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo.
echo Starting frontend server...
cd ..\frontend
start "TalkHub Frontend" cmd /k "npm start"

echo.
echo ========================================
echo TalkHub is starting up!
echo.
echo Backend:  http://localhost:5000
echo Frontend: http://localhost:3000
echo Database: SQLite (talkhub-dev.db)
echo.
echo Both servers are running in separate windows.
echo Close those windows to stop the servers.
echo ========================================
pause
